package com.lazhu.mongo.service;

import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.repository.HotNewsRepository;
import com.lazhu.system.sysparams.service.SysParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
public class HotNewsService {

    @Autowired
    private HotNewsRepository hotNewsRepository;

    @Autowired
    private SysParamsService sysParamsService;

    @Autowired
    private ReactiveMongoTemplate reactiveMongoTemplate;

    public Flux<HotNews> findHotNewsList(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findAllOrderByHeatDesc(pageable);
    }

    public Flux<HotNews> findHotNewsBySource(String source, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceOrderByHeatDesc(source, pageable);
    }

    public Flux<HotNews> searchNewsByTopic(String keyword) {
        return hotNewsRepository.findByTopicContaining(keyword);
    }

    // 新增：根据多个字段进行关键字检索
    public Flux<HotNews> searchNewsByKeyword(String keyword) {
        return hotNewsRepository.findByKeywordInMultipleFields(keyword);
    }

    // 新增：根据日期范围检索
    public Flux<HotNews> findNewsByDateRange(String startDate, String endDate) {
        return hotNewsRepository.findByDateRange(startDate, endDate);
    }

    // 新增：根据关键字和日期范围检索
    public Flux<HotNews> searchNewsByKeywordAndDateRange(String keyword, String startDate, String endDate) {
        return hotNewsRepository.findByKeywordAndDateRange(keyword, startDate, endDate);
    }

    // 新增：根据来源和关键字检索
    public Flux<HotNews> searchNewsBySourceAndKeyword(String source, String keyword) {
        return hotNewsRepository.findBySourceAndKeyword(source, keyword);
    }

    // 新增：根据类型和关键字检索
    public Flux<HotNews> searchNewsByKindAndKeyword(String kind, String keyword) {
        return hotNewsRepository.findByKindAndKeyword(kind, keyword);
    }

    // 新增：根据热度范围检索
    public Flux<HotNews> findNewsByHeatRange(String minHeat, String maxHeat) {
        return hotNewsRepository.findByHeatRange(minHeat, maxHeat);
    }

    // 新增：组合条件检索（支持分页）
    public Flux<HotNews> searchNewsByComplexConditions(String keyword, String source, String kind,
                                                       String startDate, String endDate, int page, int size) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        // 如果有关键字，搜索多个字段
        if (keyword != null && !keyword.trim().isEmpty()) {
            Criteria keywordCriteria = new Criteria().orOperator(
                    Criteria.where("topic").regex(keyword, "i"),
                    Criteria.where("source").regex(keyword, "i"),
                    Criteria.where("kind").regex(keyword, "i")
            );
            criteriaList.add(keywordCriteria);
        }

        // 如果指定了来源
        if (source != null && !source.trim().isEmpty()) {
            criteriaList.add(Criteria.where("source").is(source));
        }

        // 如果指定了类型
        if (kind != null && !kind.trim().isEmpty()) {
            criteriaList.add(Criteria.where("kind").is(kind));
        }

        // 如果指定了日期范围
        if (startDate != null && !startDate.trim().isEmpty() && endDate != null && !endDate.trim().isEmpty()) {
            criteriaList.add(Criteria.where("date").gte(startDate).lte(endDate));
        }

        // 组合所有条件
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // 添加分页和排序
        query.with(PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat")));

        return reactiveMongoTemplate.find(query, HotNews.class);
    }

    // 新增：组合条件计数（用于分页）
    public Mono<Long> countByComplexConditions(String keyword, String source, String kind,
                                              String startDate, String endDate) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        // 如果有关键字，搜索多个字段
        if (keyword != null && !keyword.trim().isEmpty()) {
            Criteria keywordCriteria = new Criteria().orOperator(
                    Criteria.where("topic").regex(keyword, "i"),
                    Criteria.where("source").regex(keyword, "i"),
                    Criteria.where("kind").regex(keyword, "i")
            );
            criteriaList.add(keywordCriteria);
        }

        // 如果指定了来源
        if (source != null && !source.trim().isEmpty()) {
            criteriaList.add(Criteria.where("source").is(source));
        }

        // 如果指定了类型
        if (kind != null && !kind.trim().isEmpty()) {
            criteriaList.add(Criteria.where("kind").is(kind));
        }

        // 如果指定了日期范围
        if (startDate != null && !startDate.trim().isEmpty() && endDate != null && !endDate.trim().isEmpty()) {
            criteriaList.add(Criteria.where("date").gte(startDate).lte(endDate));
        }

        // 组合所有条件
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        return reactiveMongoTemplate.count(query, HotNews.class);
    }

    public Flux<String> findAllSources() {
        String s = sysParamsService.queryByKey("hot_new_source_index");
        List<String> sourceIndex = Arrays.asList(s.split(","));
        //按配置的索引位置排序，没配置的排最后
        Comparator<String> comparator = (o1, o2) -> {
            int index1 = sourceIndex.indexOf(o1);
            index1 = index1 == -1 ? 100 : index1;
            int index2 = sourceIndex.indexOf(o2);
            index2 = index2 == -1 ? 100 : index2;
            // 按索引位置排序
            return Integer.compare(index1, index2);
        };
        return hotNewsRepository.findDistinctSources().map(HotNews::getSource).distinct().sort(comparator);
    }

    public Mono<Long> countHotNews() {
        return hotNewsRepository.count();
    }

    public Mono<Long> countHotNewsBySource(String source) {
        return hotNewsRepository.countBySource(source);
    }
}