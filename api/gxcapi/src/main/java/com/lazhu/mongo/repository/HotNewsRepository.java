package com.lazhu.mongo.repository;

import com.lazhu.mongo.entity.HotNews;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface HotNewsRepository extends ReactiveMongoRepository<HotNews, String> {

    @Query("{'topic': {'$regex': ?0, '$options': 'i'}}")
    Flux<HotNews> findByTopicContaining(String keyword);

    @Query("{}")
    Flux<HotNews> findAllOrderByHeatDesc(Pageable pageable);

    @Query("{'source': ?0}")
    Flux<HotNews> findBySourceOrderByHeatDesc(String source, Pageable pageable);

    @Query(value = "{}", fields = "{'source': 1}")
    Flux<HotNews> findDistinctSources();

    Mono<Long> countBySource(String source);

    // 新增：根据多个字段进行关键字检索
    @Query("{'$or': [" +
            "{'topic': {'$regex': ?0, '$options': 'i'}}, " +
            "{'source': {'$regex': ?0, '$options': 'i'}}, " +
            "{'kind': {'$regex': ?0, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findByKeywordInMultipleFields(String keyword);

    // 新增：根据日期范围检索
    @Query("{'date': {'$gte': ?0, '$lte': ?1}}")
    Flux<HotNews> findByDateRange(String startDate, String endDate);

    // 新增：根据关键字和日期范围检索
    @Query("{'$and': [" +
            "{'$or': [" +
            "{'topic': {'$regex': ?0, '$options': 'i'}}, " +
            "{'source': {'$regex': ?0, '$options': 'i'}}, " +
            "{'kind': {'$regex': ?0, '$options': 'i'}}" +
            "]}, " +
            "{'date': {'$gte': ?1, '$lte': ?2}}" +
            "]}")
    Flux<HotNews> findByKeywordAndDateRange(String keyword, String startDate, String endDate);

    // 新增：根据来源和关键字检索
    @Query("{'$and': [" +
            "{'source': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findBySourceAndKeyword(String source, String keyword);

    // 新增：根据类型和关键字检索
    @Query("{'$and': [" +
            "{'kind': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findByKindAndKeyword(String kind, String keyword);

    // 新增：根据热度范围检索
    @Query("{'heat': {'$gte': ?0, '$lte': ?1}}")
    Flux<HotNews> findByHeatRange(String minHeat, String maxHeat);


}