package com.lazhu.web.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorQuery;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.business.llmvoicejoin.service.LlmVoiceJoinService;

import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;
import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.business.mediaassets.service.MediaAssetsVideoService;
import com.lazhu.business.mediaassets.service.UserMediaAssetsService;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import com.lazhu.common.utils.MediaUtil;
import com.lazhu.baseai.llm.dto.VideoCreateResp;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.support.base.BaseService;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.web.dto.MediaAssertsDTO;
import com.lazhu.web.dto.MediaAssertsQuery;
import com.lazhu.web.dto.MediaAssetsCount;
import com.lazhu.web.mapper.ApiMediaAssertsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 素材服务
 */
@Slf4j
@Service
public class MediaAssertsService {

    @Autowired
    private ApiMediaAssertsMapper apiMediaAssertsMapper;

    @Autowired
    private UserMediaAssetsService userMediaAssetsService;

    @Autowired
    private MediaAssetsVideoService mediaAssetsVideoService;


    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private LLMService llmservice;

    @Autowired
    private LlmVoiceJoinService llmVoiceJoinService;

    @Autowired
    private ActorService actorService;

    /**
     * 查询素材列表
     */
    public Page<MediaAssertsDTO> queryAssertsList(MediaAssertsQuery query) {
        //参数封装
        Page<MediaAssertsDTO> page = BaseService.getPage(BeanUtil.beanToMap(query));
        List<Actor> actors = Collections.emptyList();
        if (StrUtil.isNotBlank(query.getActorName())) {
            ActorQuery actorQuery = new ActorQuery();
            actorQuery.setNickName(query.getActorName());
            actorQuery.setCreateBy(query.getUserId());
            actors = actorService.queryList(BeanUtil.beanToMap(actorQuery));
            if (CollUtil.isEmpty(actors)) {
                return page;
            }
            List<Long> actorIds = actors.stream().map(Actor::getId).toList();
            query.setActorIds(actorIds);
        }

        //查询
        List<MediaAssertsDTO> dtoList = new ArrayList<>();
        if (ObjectUtil.equals(query.getSource(), 1)) {
            //系统素材
            MediaAssetTypeEnum mediaAssetType = MediaAssetTypeEnum.getMediaAssetType(query.getAssetsType());
            dtoList = switch (mediaAssetType) {
                case MediaAssetTypeEnum.IMAGE -> apiMediaAssertsMapper.queryImgAssertsList(page, query);
                case MediaAssetTypeEnum.VIDEO -> apiMediaAssertsMapper.queryVideoAssertsList(page, query);
                case MediaAssetTypeEnum.AUDIO -> apiMediaAssertsMapper.queryVoiceAssertsList(page, query);
            };
        } else if (ObjectUtil.equal(query.getSource(), 2) && query.getUserId() != null) {
            //用户上传素材
            dtoList = apiMediaAssertsMapper.queryUserAssertsList(page, query);
        }

        //封装返回
        if (CollUtil.isEmpty(actors)) {
            //设置角色名称
            List<Long> actorIds = dtoList.stream()
                    .filter(e -> e.getActorId() != null)
                    .map(MediaAssertsDTO::getActorId)
                    .collect(Collectors.toList());
            actors = actorService.selectByIds(actorIds);
        }
        if (CollUtil.isNotEmpty(actors)) {
            Map<Long, Actor> actorMap = actors.stream().collect(Collectors.toMap(Actor::getId, e -> e));
            for (MediaAssertsDTO dto : dtoList) {
                Long actorId = dto.getActorId();
                if (actorId == null) {
                    continue;
                }
                Actor actor = actorMap.get(actorId);
                if (actor == null) {
                    continue;
                }
                dto.setActorName(actor.getNickName());
            }
        }
        page.setRecords(dtoList);
        return page;
    }


    /**
     * 用户上传素材
     */
    @Transactional(rollbackFor = Exception.class)
    public UserMediaAssets saveUserAsserts(UserMediaAssets param) {
        param.setId(IdWorker.getId());
        //如果音频或者视频则获取时长和封面
        if (MediaAssetTypeEnum.AUDIO.getType().equals(param.getAssetsType())) {
            param.setDurationMs(MediaUtil.getDuration(param.getMediaUrl()));
            //上传音色
            String firstLetter = PinyinUtil.getFirstLetter(param.getTitle(), "");
            if(firstLetter.length() > 10){
                firstLetter = firstLetter.substring(0,10);
            }
            VideoCreateResp voice = llmservice.createVoice(firstLetter, param.getMediaUrl());

            //保存关联记录
            LlmVoiceJoin llmVoiceJoin = new LlmVoiceJoin();
            llmVoiceJoin.setAssetsType(2);
            llmVoiceJoin.setVoiceAssetsId(param.getId());
            llmVoiceJoin.setVoiceId(voice.getVoiceId());
            llmVoiceJoin.setLlmId(voice.getLlmId());
            llmVoiceJoinService.save(llmVoiceJoin);
        } else if (MediaAssetTypeEnum.VIDEO.getType().equals(param.getAssetsType())) {
            param.setDurationMs(MediaUtil.getDuration(param.getMediaUrl()));
            File videoCover = MediaUtil.getVideoCover(param.getMediaUrl());
            if(videoCover != null && videoCover.exists()){
                String url = ossUtil.upload(videoCover);
                param.setCoverImg(url);
                //删除临时文件
                FileUtil.del(videoCover);
            } else {
                log.info("获取视频封面失败");
            }
        }
        userMediaAssetsService.save(param);
        return param;
    }

    /**
     * 根据id查询视频素材yrl
     *
     * @param source  素材来源 1系统素材 2用户上传素材
     * @param videoId 视频id
     */
    public String queryVideoUrl(Integer source, Long videoId) {
        if (ObjectUtil.equals(source, 1)) {
            MediaAssetsVideo mediaAssetsVideo = mediaAssetsVideoService.queryById(videoId);
            return mediaAssetsVideo.getVideoUrl();
        } else if (ObjectUtil.equal(source, 2)) {
            //用户上传素材
            UserMediaAssets userMediaAssets = userMediaAssetsService.queryById(videoId);
            return userMediaAssets.getMediaUrl();
        }
        return null;
    }

    /**
     * 根据id删除素材
     */
    public void deleteById(Long id) {
        UserMediaAssets userMediaAssets = userMediaAssetsService.queryById(id);
        //如果是音频素材，则同时删除大模型音色关联
        if (MediaAssetTypeEnum.AUDIO.getType().equals(userMediaAssets.getAssetsType())) {
            llmVoiceJoinService.deleteByAudioId(2, userMediaAssets.getId());
        }
        userMediaAssetsService.deleteById(id);
    }

    public List<MediaAssetsCount> getMediaAssetsCount(List<Long> actorIds) {
        if(CollUtil.isEmpty(actorIds)){
            return Collections.emptyList();
        }
        return apiMediaAssertsMapper.getMediaAssetsCount(actorIds);
    }
}
