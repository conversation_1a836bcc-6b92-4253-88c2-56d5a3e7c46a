package com.lazhu.web.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.service.HotNewsService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.web.dto.HotTopicsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 热点话题api
 */
@RestController
@RequestMapping("/api/ht")
public class HotTopicsController extends AbstractController {

    @Autowired
    private HotNewsService hotNewsService;


    /**
     * 获取所有数据来源
     *
     * @return 数据来源列表
     */
    @GetMapping("/sources")
    public Mono<ArrayResponse<String>> getAllSources() {
        return hotNewsService.findAllSources()
                .collectList()
                .map(source -> new ArrayResponseBuilder<>(source).success().bulider());
    }


    /**
     * 获取热点话题（从MongoDB查询）
     *
     * @return 列表
     */
    @GetMapping("/list")
    public Mono<PageResponse<HotTopicsDTO>> hotTopicsList(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "platSource", required = false) String source) {
        Flux<HotNews> hotNews;
        Mono<Long> total;
        if (StrUtil.isNotBlank(source)) {
            hotNews = hotNewsService.findHotNewsBySource(source, page-1, size);
            total = hotNewsService.countHotNewsBySource(source);
        } else {
            hotNews = hotNewsService.findHotNewsList(page-1, size);
            total = hotNewsService.countHotNews();
        }
        return hotNews.map(HotTopicsDTO::convertToHotTopics)
                .collectList()
                .zipWith(total)
                .map(tuple -> {
                    Page<HotTopicsDTO> p = new Page<>(page, size, tuple.getT2());
                    p.setRecords(tuple.getT1());
                    return new PageResponseBuilder<>(p).success().builder();
                });
    }

    /**
     * 搜索热点话题（仅搜索标题）
     *
     * @return 列表
     */
    @GetMapping("/search")
    public Mono<ArrayResponse<HotTopicsDTO>> searchHotNews(@RequestParam("keyword") String keyword) {
        return hotNewsService.searchNewsByTopic(keyword)
                .map(HotTopicsDTO::convertToHotTopics)
                .collectList()
                .map(data -> new ArrayResponseBuilder<>(data).success().bulider());
    }
}
